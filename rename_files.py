#!/usr/bin/env python3
"""
重命名以"复件"开头的PDF文件为invoice+数字的格式
"""

import os
import re


def rename_duplicate_pdfs():
    """
    将以"复件"开头的PDF文件重命名为invoice+数字的格式
    """
    current_dir = "."
    
    # 获取所有以"复件"开头的PDF文件
    pdf_files = []
    for filename in os.listdir(current_dir):
        if filename.startswith("复件") and filename.lower().endswith(".pdf"):
            pdf_files.append(filename)
    
    # 按文件名排序，确保重命名的一致性
    pdf_files.sort()
    
    print(f"找到 {len(pdf_files)} 个以'复件'开头的PDF文件:")
    for i, filename in enumerate(pdf_files, 1):
        print(f"{i}. {filename}")
    
    # 确认是否继续
    response = input("\n是否继续重命名这些文件？(y/n): ")
    if response.lower() != 'y':
        print("操作已取消")
        return
    
    # 重命名文件
    renamed_count = 0
    for i, old_filename in enumerate(pdf_files, 1):
        new_filename = f"invoice{i}.pdf"
        old_path = os.path.join(current_dir, old_filename)
        new_path = os.path.join(current_dir, new_filename)
        
        # 检查新文件名是否已存在
        if os.path.exists(new_path):
            print(f"警告: {new_filename} 已存在，跳过 {old_filename}")
            continue
        
        try:
            os.rename(old_path, new_path)
            print(f"重命名: {old_filename} -> {new_filename}")
            renamed_count += 1
        except OSError as e:
            print(f"错误: 无法重命名 {old_filename}: {e}")
    
    print(f"\n重命名完成！成功重命名了 {renamed_count} 个文件")


if __name__ == "__main__":
    rename_duplicate_pdfs()
